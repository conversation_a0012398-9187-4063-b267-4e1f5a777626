#!/usr/bin/env python3
"""
Test file to convert agentic schema to transition schema and save results in markdown.
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the workflow-service app directory to the path
workflow_service_path = Path(__file__).parent / "workflow-service" / "app"
sys.path.insert(0, str(workflow_service_path))

try:
    from services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
    from services.workflow_builder.node_combiner import combine_nodes
except ImportError as e:
    print(f"❌ Error importing conversion modules: {e}")
    print(f"Make sure you're running this script from the backend directory")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Looking for modules in: {workflow_service_path}")
    sys.exit(1)


def load_agentic_schema(file_path: str) -> dict:
    """Load the agentic schema from JSON file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {file_path}: {e}")
        sys.exit(1)


def save_transition_schema(schema: dict, output_path: str) -> None:
    """Save the transition schema to JSON file."""
    try:
        with open(output_path, 'w') as f:
            json.dump(schema, f, indent=2)
        print(f"✅ Transition schema saved to: {output_path}")
    except Exception as e:
        print(f"❌ Error saving transition schema: {e}")
        sys.exit(1)


def save_conversion_report_md(agentic_schema: dict, transition_schema: dict, output_path: str) -> None:
    """Save a detailed conversion report in markdown format."""
    try:
        workflow_data = agentic_schema.get("workflow_data", {})
        nodes = transition_schema.get('nodes', [])
        transitions = transition_schema.get('transitions', [])

        # Generate markdown content
        md_content = f"""# Agentic to Transition Schema Conversion Report

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This report details the conversion of an agentic workflow schema to a transition schema format for execution by the orchestration engine.

### Input Schema Summary
- **Workflow Name**: {agentic_schema.get('name', 'Unknown')}
- **Description**: {agentic_schema.get('description', 'No description')}
- **Original Nodes**: {len(workflow_data.get('nodes', []))}
- **Original Edges**: {len(workflow_data.get('edges', []))}

### Output Schema Summary
- **Transition Nodes**: {len(nodes)}
- **Transitions**: {len(transitions)}

## Node Analysis

### Original Nodes
"""

        # Analyze original nodes
        original_nodes = workflow_data.get('nodes', [])
        node_types = {}
        for node in original_nodes:
            original_type = node.get('data', {}).get('originalType', 'unknown')
            if original_type not in node_types:
                node_types[original_type] = []
            node_types[original_type].append(node.get('id', 'unknown'))

        for node_type, node_ids in node_types.items():
            md_content += f"\n#### {node_type} ({len(node_ids)} nodes)\n"
            for node_id in node_ids:
                md_content += f"- `{node_id}`\n"

        # Analyze transition nodes
        md_content += "\n### Transition Nodes\n"
        server_types = {}
        for node in nodes:
            server_script_path = node.get('server_script_path', 'unknown')
            if server_script_path not in server_types:
                server_types[server_script_path] = []
            server_types[server_script_path].append(node.get('id', 'unknown'))

        for server_type, node_ids in server_types.items():
            md_content += f"\n#### {server_type} ({len(node_ids)} nodes)\n"
            for node_id in node_ids:
                md_content += f"- `{node_id}`\n"

        # Analyze transitions
        md_content += "\n## Transition Analysis\n"

        transition_types = {}
        execution_types = {}

        for transition in transitions:
            t_type = transition.get('transition_type', 'unknown')
            e_type = transition.get('execution_type', 'unknown')

            if t_type not in transition_types:
                transition_types[t_type] = []
            transition_types[t_type].append(transition.get('id', 'unknown'))

            if e_type not in execution_types:
                execution_types[e_type] = []
            execution_types[e_type].append(transition.get('id', 'unknown'))

        md_content += "\n### By Transition Type\n"
        for t_type, transition_ids in transition_types.items():
            md_content += f"\n#### {t_type} ({len(transition_ids)} transitions)\n"
            for transition_id in transition_ids:
                md_content += f"- `{transition_id}`\n"

        md_content += "\n### By Execution Type\n"
        for e_type, transition_ids in execution_types.items():
            md_content += f"\n#### {e_type} ({len(transition_ids)} transitions)\n"
            for transition_id in transition_ids:
                md_content += f"- `{transition_id}`\n"

        # Detailed transition breakdown
        md_content += "\n## Detailed Transition Breakdown\n"

        for i, transition in enumerate(transitions, 1):
            transition_id = transition.get('id', f'transition-{i}')
            sequence = transition.get('sequence', 'unknown')
            t_type = transition.get('transition_type', 'unknown')
            e_type = transition.get('execution_type', 'unknown')
            is_end = transition.get('end', False)

            md_content += f"\n### {i}. {transition_id}\n"
            md_content += f"- **Sequence**: {sequence}\n"
            md_content += f"- **Transition Type**: {t_type}\n"
            md_content += f"- **Execution Type**: {e_type}\n"
            md_content += f"- **Is End**: {is_end}\n"

            # Node info
            node_info = transition.get('node_info', {})
            node_id = node_info.get('node_id', 'unknown')
            tools = node_info.get('tools_to_use', [])
            input_data = node_info.get('input_data', [])
            output_data = node_info.get('output_data', [])

            md_content += f"- **Node ID**: `{node_id}`\n"
            md_content += f"- **Tools**: {len(tools)}\n"
            md_content += f"- **Input Data**: {len(input_data)}\n"
            md_content += f"- **Output Data**: {len(output_data)}\n"

            # Tool details
            if tools:
                md_content += "\n#### Tools:\n"
                for j, tool in enumerate(tools, 1):
                    tool_name = tool.get('tool_name', 'unknown')
                    tool_id = tool.get('tool_id', 'unknown')
                    md_content += f"{j}. **{tool_name}** (`{tool_id}`)\n"

                    # Tool parameters
                    tool_params = tool.get('tool_params', {})
                    if tool_params and 'items' in tool_params:
                        md_content += "   - Parameters:\n"
                        for param in tool_params['items']:
                            field_name = param.get('field_name', 'unknown')
                            field_value = param.get('field_value', 'null')
                            md_content += f"     - `{field_name}`: {field_value}\n"

        # Add JSON schemas at the end
        md_content += "\n## Complete Schemas\n"
        md_content += "\n### Original Agentic Schema\n"
        md_content += "```json\n"
        md_content += json.dumps(agentic_schema, indent=2)
        md_content += "\n```\n"

        md_content += "\n### Generated Transition Schema\n"
        md_content += "```json\n"
        md_content += json.dumps(transition_schema, indent=2)
        md_content += "\n```\n"

        # Save to file
        with open(output_path, 'w') as f:
            f.write(md_content)

        print(f"✅ Conversion report saved to: {output_path}")

    except Exception as e:
        print(f"❌ Error saving conversion report: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main conversion function."""
    # Input and output file paths
    input_file = "agentic_schema.json"
    output_file = "transition_schema.json"
    report_file = "conversion_report.md"

    print("🚀 AGENTIC TO TRANSITION SCHEMA CONVERTER & TEST")
    print("=" * 60)

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        print(f"Please make sure {input_file} exists in the current directory")
        sys.exit(1)

    print(f"📂 Loading agentic schema from: {input_file}")
    agentic_schema = load_agentic_schema(input_file)

    # Extract workflow data from agentic schema
    workflow_data = agentic_schema.get("workflow_data", {})

    if not workflow_data:
        print("❌ No workflow_data found in agentic schema")
        sys.exit(1)

    print(f"📊 Agentic schema loaded successfully:")
    print(f"   - Workflow name: {agentic_schema.get('name', 'Unknown')}")
    print(f"   - Description: {agentic_schema.get('description', 'No description')}")
    print(f"   - Nodes: {len(workflow_data.get('nodes', []))}")
    print(f"   - Edges: {len(workflow_data.get('edges', []))}")

    try:
        print(f"\n🔄 Starting conversion process...")

        # Convert workflow data to transition schema
        transition_schema = convert_workflow_to_transition_schema(workflow_data)

        print(f"\n✅ Conversion completed successfully!")
        print(f"📊 Transition schema summary:")
        print(f"   - Nodes: {len(transition_schema.get('nodes', []))}")
        print(f"   - Transitions: {len(transition_schema.get('transitions', []))}")

        # Save the JSON result
        save_transition_schema(transition_schema, output_file)

        # Save the detailed markdown report
        print(f"\n📝 Generating detailed conversion report...")
        save_conversion_report_md(agentic_schema, transition_schema, report_file)

        # Print some details about the converted schema
        print(f"\n📋 Conversion details:")

        # Show node types
        nodes = transition_schema.get('nodes', [])
        node_types = {}
        for node in nodes:
            server_script_path = node.get('server_script_path', 'unknown')
            if server_script_path not in node_types:
                node_types[server_script_path] = 0
            node_types[server_script_path] += 1

        print(f"   Node types:")
        for node_type, count in node_types.items():
            print(f"     - {node_type}: {count}")

        # Show transition types
        transitions = transition_schema.get('transitions', [])
        transition_types = {}
        execution_types = {}

        for transition in transitions:
            t_type = transition.get('transition_type', 'unknown')
            e_type = transition.get('execution_type', 'unknown')

            if t_type not in transition_types:
                transition_types[t_type] = 0
            transition_types[t_type] += 1

            if e_type not in execution_types:
                execution_types[e_type] = 0
            execution_types[e_type] += 1

        print(f"   Transition types:")
        for t_type, count in transition_types.items():
            print(f"     - {t_type}: {count}")

        print(f"   Execution types:")
        for e_type, count in execution_types.items():
            print(f"     - {e_type}: {count}")

        print(f"\n🎉 Conversion complete!")
        print(f"📁 Files generated:")
        print(f"   - JSON Schema: {output_file}")
        print(f"   - Markdown Report: {report_file}")

    except Exception as e:
        print(f"\n❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
